# 项目相关配置
ruoyi:
  # 名称
  name: 邻里办API
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: false
  # 文件路径 示例（ Windows配置C:/source-vue/static/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /home/<USER>/uploadPath
  # 获取ip地址开关
  addressEnabled: true
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  # 域名，图片url前缀
  domain: http://cdn.zn.nextv.show

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8088
  port: 12345
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    cn.source: debug
    org.springframework: warn

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  10MB
       # 设置总上传的文件大小
       max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 2
    # 密码
    password: dbosshop
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 30

# MyBatis配置
#mybatis:
#    # 搜索指定包别名
#    typeAliasesPackage: cn.source.**.domain
#    # 配置mapper的扫描，找到所有的mapper.xml映射文件
#    mapperLocations: classpath*:mapper/**/*Mapper.xml
#    # 加载全局的配置文件
#    configLocation: classpath:mybatis/mybatis-config.xml
# ====================MybatisPlus====================
mybatis-plus:
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: cn.source.**.domain
  # 针对 typeAliasesPackage，如果配置了该属性，则仅仅会扫描路径下以该类作为父类的域对象
  #typeAliasesSuperType: Class<?>
  # 如果配置了该属性，SqlSessionFactoryBean 会把该包下面的类注册为对应的 TypeHandler
  #typeHandlersPackage: null
  # 如果配置了该属性，会将路径下的枚举类进行注入，让实体类字段能够简单快捷的使用枚举属性
  #typeEnumsPackage: null
  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查
  checkConfigLocation: false
  # 通过该属性可指定 MyBatis 的执行器，MyBatis 的执行器总共有三种：
  # SIMPLE：该执行器类型不做特殊的事情，为每个语句的执行创建一个新的预处理语句（PreparedStatement）
  # REUSE：该执行器类型会复用预处理语句（PreparedStatement）
  # BATCH：该执行器类型会批量执行所有的更新语句
  executorType: SIMPLE
  # 指定外部化 MyBatis Properties 配置，通过该配置可以抽离配置，实现不同环境的配置部署
  configurationProperties: null
  configuration:
    # 自动驼峰命名规则（camel case）映射
    # 如果您的数据库命名符合规则无需使用 @TableField 注解指定数据库字段名
    mapUnderscoreToCamelCase: true
    # 默认枚举处理类,如果配置了该属性,枚举将统一使用指定处理器进行处理
    # org.apache.ibatis.type.EnumTypeHandler : 存储枚举的名称
    # org.apache.ibatis.type.EnumOrdinalTypeHandler : 存储枚举的索引
    # com.baomidou.mybatisplus.extension.handlers.MybatisEnumTypeHandler : 枚举类需要实现IEnum接口或字段标记@EnumValue注解.
    defaultEnumTypeHandler: org.apache.ibatis.type.EnumTypeHandler
    # 当设置为 true 的时候，懒加载的对象可能被任何懒属性全部加载，否则，每个属性都按需加载。需要和 lazyLoadingEnabled 一起使用。
    aggressiveLazyLoading: true
    # MyBatis 自动映射策略
    # NONE：不启用自动映射
    # PARTIAL：只对非嵌套的 resultMap 进行自动映射
    # FULL：对所有的 resultMap 都进行自动映射
    autoMappingBehavior: PARTIAL
    # MyBatis 自动映射时未知列或未知属性处理策
    # NONE：不做任何处理 (默认值)
    # WARNING：以日志的形式打印相关警告信息
    # FAILING：当作映射失败处理，并抛出异常和详细信息
    autoMappingUnknownColumnBehavior: NONE
    # Mybatis一级缓存，默认为 SESSION
    # SESSION session级别缓存，同一个session相同查询语句不会再次查询数据库
    # STATEMENT 关闭一级缓存
    localCacheScope: SESSION
    # 开启Mybatis二级缓存，默认为 true
    cacheEnabled: true
  global-config:
    # 是否打印 Logo banner
    banner: true
    # 是否初始化 SqlRunner
    enableSqlRunner: false
    dbConfig:
      # 主键类型
      # AUTO 数据库ID自增
      # NONE 空
      # INPUT 用户输入ID
      # ASSIGN_ID 全局唯一ID
      # ASSIGN_UUID 全局唯一ID UUID
      idType: AUTO
      # 表名前缀
      tablePrefix: null
      # 字段 format,例: %s,(对主键无效)
      columnFormat: null
      # 表名是否使用驼峰转下划线命名,只对表名生效
      tableUnderline: true
      # 大写命名,对表名和字段名均生效
      capitalMode: false
      # 全局的entity的逻辑删除字段属性名
      logicDeleteField: null
      # 逻辑已删除值
      logicDeleteValue: 2
      # 逻辑未删除值
      logicNotDeleteValue: 0
      # 字段验证策略之 insert,在 insert 的时候的字段验证策略
      # IGNORED 忽略判断
      # NOT_NULL 非NULL判断
      # NOT_EMPTY 非空判断(只对字符串类型字段,其他类型字段依然为非NULL判断)
      # DEFAULT 默认的,一般只用于注解里
      # NEVER 不加入 SQL
      insertStrategy: NOT_NULL
      # 字段验证策略之 update,在 update 的时候的字段验证策略
      updateStrategy: NOT_NULL
      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件
      selectStrategy: NOT_NULL
# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/system/item,/system/article,/system/goods
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# Flowable 配置
flowable:
  # 关闭定时任务 job
  async-executor-activate: false
  # 库与数据库表结构不一致时，会自动将数据库表结构升级至新版本。
  database-schema-update: true
  idm:
    enabled: false

# sms 短信
sms:
  id: xinyuankeji
  pwd: a4a526db17470cb94f9787a7abac8220
  template: 573517

# wechat 微信
wechat:
  # 公众号
  mp:
    # 生活助手
    appId: 换你自己的
    secret: 换你自己的
    accessTokenKey: MP-ACCESS-TOKEN-AUTH
  # 小程序
  mini:
    appId: wx2369dda039c7aaec
    secret: 04bd112497fb7451ccb8fe99ff8d9ab6
    accessTokenKey: MINI-ACCESS-TOKEN-AUTH

#s3
aws:
  accessKey: ee4NNl7RKfhgKLvGzNdJkkH2uNDUmxQHpg9TU8Qk
  secretKey: 5xDOAXod5bZ6nMKeRC6xemZDlneMmACa4si0CdHF
  bucket: cdn.zn.nextv.show
  endpoint: eos-chengdu-6.cmecloud.cn
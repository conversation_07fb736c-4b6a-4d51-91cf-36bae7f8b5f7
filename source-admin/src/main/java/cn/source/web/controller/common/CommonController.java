package cn.source.web.controller.common;

import cn.source.common.config.RuoYiConfig;
import cn.source.common.constant.Constants;
import cn.source.common.core.domain.AjaxResult;
import cn.source.common.utils.AwsS3Utils;
import cn.source.common.utils.StringUtils;
import cn.source.common.utils.file.FileUploadUtils;
import cn.source.common.utils.file.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RequestMapping("/common")
@RestController
public class CommonController {
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Resource
    AwsS3Utils s3Utils;

    @Value("${ruoyi.domain}")
    private String domain;

    @Value("${aws.bucket}")
    private String bucket;
    @Value("${aws.endpoint}")
    private String endpoint;

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete   是否删除
     */
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        try {
            if (!FileUtils.checkAllowDownload(fileName)) {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete) {
                FileUtils.deleteFile(filePath);
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        try {
            // 真实文件名
            String realName = file.getOriginalFilename();
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = domain + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            ajax.put("realName", realName);
            ajax.put("url", url);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        try {
            if (!FileUtils.checkAllowDownload(resource)) {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = RuoYiConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * S3预签名上传地址
     */
    @GetMapping("/generatePresignedUrl")
    public AjaxResult generatePresignedUrl(String key) throws Exception {
        try {
            String url = s3Utils.generatePresignedUrl(key);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            return ajax;
        } catch (Exception e) {
            log.error("下载文件失败", e);
            return AjaxResult.error("系统繁忙");
        }
    }

    /**
     * S3预签名上传地址
     */
    @GetMapping("=/generatePath")
    public AjaxResult generatePath(String type) throws Exception {
        try {
            String url = s3Utils.generatePath(type);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            return ajax;
        } catch (Exception e) {
            log.error("下载文件失败", e);
            return AjaxResult.error("系统繁忙");
        }
    }

    //    @PostMapping(value = "=/uploadObjectOSS", produces = {MediaType.APPLICATION_JSON_UTF8_VALUE}, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PostMapping("/uploadObjectOSS")
    public AjaxResult uploadObjectOSS(MultipartFile file) throws IOException {
        //空文件限制
        if (file.isEmpty()) {
            return AjaxResult.error("上传文件不能为空！");
        }
        // 真实文件名
        String realName = file.getOriginalFilename();
        String fileKey = FileUploadUtils.extractFilename(file);
        String upload = s3Utils.upload(file.getInputStream(), fileKey);
        log.info("文件---[{}]---上传到S3服务器成功！", fileKey);
        //文件访问路径
        String url = domain + "/" + fileKey;
        AjaxResult ajax = AjaxResult.success();
        ajax.put("url", url);
        ajax.put("fileName", fileKey);
        ajax.put("realName", realName);
        return ajax;
    }
}

package cn.source.web.controller.system;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.source.common.core.controller.BaseController;
import cn.source.common.core.domain.AjaxResult;
import cn.source.common.core.domain.entity.SysDictData;
import cn.source.common.core.domain.entity.SysUser;
import cn.source.common.core.page.TableDataInfo;
import cn.source.common.utils.SecurityUtils;
import cn.source.common.utils.ServletUtils;
import cn.source.common.utils.ip.AddressUtils;
import cn.source.common.utils.ip.IpUtils;
import cn.source.framework.web.service.TokenService;
import cn.source.system.domain.SysAboutUs;
import cn.source.system.domain.SysNotice;
import cn.source.system.domain.SysUserCategory;
import cn.source.system.enums.CertStatus;
import cn.source.system.service.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基础服务接口
 *
 * <AUTHOR>
 */
@RequestMapping("/api")
@RestController
public class SysApiController extends BaseController {

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysNoticeService noticeService;

    @Resource
    private ISysAboutUsService aboutUsService;

    @Resource
    private ISysUserCategoryService userCategoryService;

    @Resource
    private ISysDictDataService dictDataService;

    @Autowired
    private TokenService tokenService;

    /**
     * 通过IP获取到城市信息
     */
    @GetMapping("/profile/getRealCity")
    public AjaxResult getRealCity() {
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        String address = AddressUtils.getRealCityByIP(ip);
        return AjaxResult.success(address);
    }

    /**
     * 通过IP获取到城市信息
     */
    @GetMapping("/profile/getRealCityByIP")
    public AjaxResult getRealCityByIP(String ip) {
        String address = AddressUtils.getRealCityByIP(ip);
        return AjaxResult.success(address);
    }

    /**
     * 验证令牌是否过期
     */
    @GetMapping("/profile/isExpiration")
    public AjaxResult isExpiration(String token) {
        return AjaxResult.success(tokenService.isExpiration(token));
    }

    /**
     * profile 重置密码
     */
    @PutMapping("/profile/updatePwd")
    public AjaxResult updatePwd(@RequestBody JSONObject json) {
        String oldPassword = json.getString("oldPassword");
        String newPassword = json.getString("newPassword");
        Long userId = json.getLong("userId");
        SysUser sysUser = userService.selectUserById(userId);
        String userName = sysUser.getUserName();
        String password = sysUser.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password)) {
            return AjaxResult.error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password)) {
            return AjaxResult.error("新密码不能与旧密码相同");
        }
        if (userService.resetUserPwd(userName, SecurityUtils.encryptPassword(newPassword)) > 0) {
            return AjaxResult.success();
        }
        return AjaxResult.error("修改密码异常，请联系管理员");
    }

    /**
     * profile 修改用户信息
     */
    @PutMapping("/profile/updateProfile")
    public AjaxResult updateProfile(@RequestBody JSONObject json) {
        String nickName = json.getString("nickName");
        String avatar = json.getString("avatar");
        Long userId = json.getLong("userId");
        SysUser sysUser = userService.selectUserById(userId);
        sysUser.setNickName(nickName);
        sysUser.setAvatar(avatar);
        if (userService.updateUserProfile(sysUser) > 0) {
            return AjaxResult.success();
        }
        return AjaxResult.error("修改个人信息异常，请联系管理员");
    }

    /**
     * profile 实名认证
     */
    @PostMapping("/profile/certification")
    public AjaxResult certification(@RequestBody JSONObject json) {
        String room = json.getString("room");
        Long houseId = json.getLong("houseId");
        String house = json.getString("house");
        String realname = json.getString("realname");
        String idCard = json.getString("idCard");
        Long userId = json.getLong("userId");
        SysUser sysUser = userService.selectUserById(userId);
        if (!CertStatus.AUDIT.getCode().equals(sysUser.getCertStatus())) {
            return AjaxResult.error("无需重复认证");
        }
        sysUser.setHouseId(houseId);
        sysUser.setHouse(house);
        sysUser.setRoom(room);
        sysUser.setRealname(realname);
        sysUser.setIdCard(idCard);
        sysUser.setCertStatus(CertStatus.AUDITING.getCode());
        if (userService.updateUserProfile(sysUser) > 0) {
            return AjaxResult.success();
        }
        return AjaxResult.error("修改个人信息异常，请联系管理员");
    }

    /**
     * 个人信息
     */
    @GetMapping("/profile")
    public AjaxResult profile(Long userId) {
        Map<String, Object> ret = new HashMap<>();
        SysUser user = userService.selectUserById(userId);
        if ("0".equals(user.getUserType())) {
            List<Long> categoryList = userCategoryService.list(new LambdaQueryWrapper<SysUserCategory>().eq(SysUserCategory::getUserId, userId)).stream().map(SysUserCategory::getCId).collect(Collectors.toList());
            SysDictData sysDictData = new SysDictData();
            sysDictData.setDictType("sys_message_category");
            List<SysDictData> dictDataList = dictDataService.selectDictDataList(sysDictData);
            List<SysDictData> dictData = dictDataList.stream().filter(item -> categoryList.contains(Convert.toLong(item.getDictValue()))).collect(Collectors.toList());
            ret.put("categoryList", dictData);
        }
        BeanUtil.copyProperties(user, ret);
        return AjaxResult.success(ret);
    }

    /**
     * 社区人员信息
     */
    @GetMapping("/community/user")
    public AjaxResult communityUserList() {
        SysUser sysUser = new SysUser();
        sysUser.setUserType("0");
        sysUser.setStatus("0");
        sysUser.setDeptId(102L);
        List<SysUser> sysUsers = userService.selectUserList(sysUser);
        List<Map<String, Object>> ret = new ArrayList<>(sysUsers.size());
        for (SysUser user : sysUsers) {
            Map<String, Object> map = new HashMap<>();
            List<Long> categoryList = userCategoryService.list(new LambdaQueryWrapper<SysUserCategory>().eq(SysUserCategory::getUserId, user.getUserId())).stream().map(SysUserCategory::getCId).collect(Collectors.toList());
            SysDictData sysDictData = new SysDictData();
            sysDictData.setDictType("sys_message_category");
            List<SysDictData> dictDataList = dictDataService.selectDictDataList(sysDictData);
            List<String> categoryArr = dictDataList.stream().filter(item -> categoryList.contains(Convert.toLong(item.getDictValue()))).map(item -> item.getDictLabel()).collect(Collectors.toList());
            String categoryStr = StringUtils.join(categoryArr, ",");
            map.put("phonenumber", user.getPhonenumber());
            map.put("nickName", user.getNickName());
            map.put("category", categoryStr);
            ret.add(map);
        }
        return AjaxResult.success(ret);
    }

    /**
     * @Description: 获取通知列表
     */
    @GetMapping("/notice/findNoticeList")
    public TableDataInfo findNoticeList(SysNotice notice) {
        startPage();
        List<SysNotice> list = noticeService.selectNoticeList(notice);
        return getDataTable(list);
    }

    /**
     * @Description: 获取关于我们
     */
    @GetMapping("/about/getAboutByType/{type}")
    public AjaxResult getAboutByType(@PathVariable("type") String type) {
        SysAboutUs about = aboutUsService.getOne(new LambdaQueryWrapper<SysAboutUs>().eq(SysAboutUs::getType, type));
        return AjaxResult.success(about);
    }

    /**
     * 获取文章详细信息
     */
    @GetMapping(value = "/notice/getNotice/{id}")
    public AjaxResult getNotice(@PathVariable("id") Long id) {
        return AjaxResult.success(noticeService.selectNoticeById(id));
    }

}

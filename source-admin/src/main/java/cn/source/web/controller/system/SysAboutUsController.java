package cn.source.web.controller.system;

import cn.source.common.annotation.Log;
import cn.source.common.core.controller.BaseController;
import cn.source.common.core.domain.AjaxResult;
import cn.source.common.core.page.TableDataInfo;
import cn.source.common.enums.BusinessType;
import cn.source.system.domain.SysAboutUs;
import cn.source.system.service.ISysAboutUsService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/system/about")
public class SysAboutUsController extends BaseController {

    @Resource
    private ISysAboutUsService aboutUsService;

    /**
     * 获取关于我们列表
     */
    @PreAuthorize("@ss.hasPermi('system:about:list')")
    @GetMapping("/list")
    public TableDataInfo list() {
        List<SysAboutUs> list = aboutUsService.list();
        return getDataTable(list);
    }

    /**
     * 根据关于我们编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:about:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(aboutUsService.getById(id));
    }

    /**
     * 修改关于我们
     */
    @PreAuthorize("@ss.hasPermi('system:notice:edit')")
    @Log(title = "关于我们", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysAboutUs form) {
        return toAjax(aboutUsService.updateById(form));
    }
}

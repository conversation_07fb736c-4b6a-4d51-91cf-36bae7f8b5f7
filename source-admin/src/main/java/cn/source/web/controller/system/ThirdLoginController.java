package cn.source.web.controller.system;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import cn.source.common.constant.Constants;
import cn.source.common.core.controller.BaseController;
import cn.source.common.core.domain.AjaxResult;
import cn.source.common.core.domain.entity.SysUser;
import cn.source.common.core.domain.model.LoginBody;
import cn.source.common.core.domain.model.LoginUser;
import cn.source.common.core.redis.RedisCache;
import cn.source.common.enums.UserType;
import cn.source.common.utils.SecurityUtils;
import cn.source.common.utils.StringUtils;
import cn.source.framework.web.service.SysLoginService;
import cn.source.framework.web.service.SysPermissionService;
import cn.source.framework.web.service.TokenService;
import cn.source.system.service.ISysNoticeService;
import cn.source.system.service.ISysUserService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import org.springframework.web.client.RestTemplate; // 用于发送HTTP请求
import org.springframework.http.HttpEntity; // 用于封装请求和响应的数据
import org.springframework.http.HttpHeaders; // 用于设置HTTP请求的头部信息
import org.springframework.http.MediaType; // 用于定义媒体类型
import org.json.JSONObject; // 用于构建JSON对象

import javax.servlet.http.HttpServletRequest;
import java.util.Set;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import javax.crypto.KeyGenerator;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * 三方登录验证
 *
 * <AUTHOR>
 */
@RequestMapping("/api")
@RestController
public class ThirdLoginController extends BaseController {
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysNoticeService noticeService;

    @Value("${wechat.mini.appId}")
    private String APPID;

    @Value("${wechat.mini.secret}")
    private String SECRET;

    private static final String AES_KEY = "wanan_aes_34E9f5ceA34282Pxa385"; // 签名密钥

    /**
     * 居民账号密码登录
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/resident/passwordLogin")
    public AjaxResult passwordLogin(@RequestBody LoginBody loginBody) {
        return loginService.thirdLogin(loginBody.getUsername(), loginBody.getPassword());
    }

    /**
     * 手机注册/登录
     */
    @PostMapping("/resident/smsLogin")
    public AjaxResult smsLogin(HttpServletRequest request,
            @RequestBody LoginBody loginBody) {
        AjaxResult ajax = AjaxResult.success();
        String msg = "登录成功";
        if (StringUtils.isEmpty(loginBody.getUsername()) || StringUtils.isEmpty(loginBody.getCode())) {
            msg = "用户名/验证码不能为空";
            return error(msg);
        }
        // 首先验证验证码是否正确
        if (redisCache.getCacheObject(loginBody.getUsername()) == null
                || !redisCache.getCacheObject(loginBody.getUsername()).equals(loginBody.getCode())) {
            msg = "验证码过期/错误";
            return error(msg);
        }
        // 验证码正确则判断是否为新用户
        SysUser sysUser = userService.selectUserByUserName(loginBody.getUsername());
        // 不是新用户，创建用户
        if (sysUser == null) {
            sysUser = new SysUser();
            sysUser.setUserName(loginBody.getUsername());
            sysUser.setNickName(loginBody.getUsername());
            sysUser.setPhonenumber(loginBody.getUsername());
            sysUser.setDeptId(101L);
            sysUser.setUserType(UserType.RESIDENT.getCode());
            userService.insertUser(sysUser);
        }
        // 生成token
        LoginUser loginUser = new LoginUser(sysUser, null);
        String token = tokenService.createToken(loginUser);
        ajax.put(Constants.TOKEN, token);
        ajax.put("loginUser", loginUser);
        return ajax;
    }

    /**
     * 微信小程序注册/登录
     */
    @PostMapping("/weChatLogin")
    public AjaxResult weChatLogin(HttpServletRequest request, @RequestBody LoginBody loginBody) {
        AjaxResult ajax = AjaxResult.success();
        String msg = "登录成功";
        if (StringUtils.isEmpty(loginBody.getUsername())) {
            msg = "用户名不能为空";
            return error(msg);
        }

        // 从请求中获取code
        String code = loginBody.getCode();
        if (StringUtils.isEmpty(code)) {
            return error("code 不能为空");
        }

        // 通过code获取openId
        SessionInfo sessionInfo = getIdFromCode(code);
        String openId = sessionInfo.getOpenId();
        String unionId = sessionInfo.getUnionId();
        if (openId == null) {
            return error("无法获取 openId");
        }
        if (unionId == null) {
            return error("无法获取 unionId");
        }

        // AES 加密 openId
        try {
            String encryptedOpenId = encryptWithAES(unionId, AES_KEY);
            ajax.put("encryptedOpenId", encryptedOpenId); // 将加密的 openId 添加到响应中
        } catch (Exception e) {
            e.printStackTrace();
            return error(openId + "====" + unionId + "==OpenId 加密失败: " + e.getMessage());
        }

        // 判断是否为新用户
        SysUser sysUser = userService.selectUserByUserName(loginBody.getUsername());
        // 不是新用户，创建用户
        if (sysUser == null) {
            sysUser = new SysUser();
            sysUser.setUserName(loginBody.getUsername());
            sysUser.setNickName(loginBody.getUsername());
            sysUser.setPhonenumber(loginBody.getUsername());
            sysUser.setDeptId(101L);
            sysUser.setUserType(UserType.RESIDENT.getCode());

            //保存uinonid和openid
            sysUser.setOpenId(openId);
            sysUser.setUnionId(unionId);
            userService.insertUser(sysUser);
        }
        // 生成token
        LoginUser loginUser = new LoginUser(sysUser, null);
        String token = tokenService.createToken(loginUser);
        ajax.put(Constants.TOKEN, token);
        ajax.put("loginUser", loginUser);
        ajax.put("openId", openId); // 将 openId 添加到响应中
        ajax.put("unionId", unionId); // 将 unionId 添加到响应中

        //如果用户已经存在，并且unionid为空，则保存uinonid和openid
        //主要针对已经存在的历史用户，但是没有unionid的情况
        if (sysUser.getUnionId() == null) {
            sysUser.setOpenId(openId);
            sysUser.setUnionId(unionId);
            userService.updateUser(sysUser);
        }

        // // 将token和unionId同步到得宝电商平台
        // try {
        //     syncUserInfoToDbosshop(token, unionId);
        // } catch (Exception e) {
        //     e.printStackTrace();
        //     // 根据实际情况处理异常，例如记录日志、返回错误信息等
        // }
        return ajax;
    }


    private SessionInfo  getIdFromCode(String code) {
    
        String openId = null;
        String unionId = null;
        WxMaJscode2SessionResult wxMaJscode2SessionResult = getSessionInfoFromCode(code);
        openId = wxMaJscode2SessionResult.getOpenid();
        unionId = wxMaJscode2SessionResult.getUnionid();
        return new SessionInfo(openId, unionId);
    }

    private WxMaJscode2SessionResult getSessionInfoFromCode(String code) {
        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        config.setAppid(APPID);
        config.setSecret(SECRET);
        WxMaService service = new WxMaServiceImpl();
        service.setWxMaConfig(config);

        try {
            return service.jsCode2SessionInfo(code);
        } catch (WxErrorException e) {
            e.printStackTrace();
            return null; // 或者根据需求返回错误信息
        }
    }

    public static String encryptWithAES(String plaintext, String password) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        SecretKey secretKey = generateAESKey(password);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    private static SecretKey generateAESKey(String password) throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
        keyGenerator.init(128);
        byte[] passwordBytes = password.getBytes(StandardCharsets.UTF_8);
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] keyBytes = digest.digest(passwordBytes);
        return new SecretKeySpec(keyBytes, "AES");
    }

    /**
     * 绑定微信
     */
    @GetMapping("/bindWechat")
    public AjaxResult bindWechat(Long userId, String code) throws WxErrorException {
        SysUser user = userService.selectUserById(userId);
        AjaxResult ajax = AjaxResult.success();
        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        config.setAppid(APPID);
        config.setSecret(SECRET);
        WxMaService service = new WxMaServiceImpl();
        service.setWxMaConfig(config);

        WxMaJscode2SessionResult jscode2session = service.jsCode2SessionInfo(code);

        user.setOpenId(jscode2session.getOpenid());
        user.setUnionId(jscode2session.getUnionid());
        user.setSessionKey(jscode2session.getSessionKey());
        userService.updateUserProfile(user);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    public class SessionInfo {
        private String openId;
        private String unionId;
    
        public SessionInfo(String openId, String unionId) {
            this.openId = openId;
            this.unionId = unionId;
        }
    
        public String getOpenId() {
            return openId;
        }
    
        public String getUnionId() {
            return unionId;
        }
    }
}

package cn.source.system.service.impl;

import cn.source.system.domain.UserMessageDiscussMedia;
import cn.source.system.mapper.UserMessageDiscussMediaMapper;
import cn.source.system.service.IUserMessageDiscussMediaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 信息图片Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class UserMessageDiscussMediaImpl extends ServiceImpl<UserMessageDiscussMediaMapper, UserMessageDiscussMedia> implements IUserMessageDiscussMediaService {

}

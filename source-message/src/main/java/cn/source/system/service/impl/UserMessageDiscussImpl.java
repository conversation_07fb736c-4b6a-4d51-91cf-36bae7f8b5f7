package cn.source.system.service.impl;

import cn.source.system.domain.UserMessageDiscuss;
import cn.source.system.mapper.UserMessageDiscussMapper;
import cn.source.system.service.IUserMessageDiscussService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 用户消息Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class UserMessageDiscussImpl extends ServiceImpl<UserMessageDiscussMapper, UserMessageDiscuss> implements IUserMessageDiscussService {

}

package cn.source.system.service.impl;

import cn.source.system.domain.UserMessageMedia;
import cn.source.system.mapper.UserMessageImageMapper;
import cn.source.system.service.IUserMessageImageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 信息图片Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class UserMessageImageImpl extends ServiceImpl<UserMessageImageMapper, UserMessageMedia> implements IUserMessageImageService {

}

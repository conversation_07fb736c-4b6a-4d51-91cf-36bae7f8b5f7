package cn.source.system.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaMsgService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import cn.hutool.core.date.DateUtil;
import cn.source.common.core.domain.entity.SysUser;
import cn.source.common.utils.StringUtils;
import cn.source.system.domain.SysUserCategory;
import cn.source.system.domain.UserMessage;
import cn.source.system.mapper.UserMessageMapper;
import cn.source.system.service.ISysUserCategoryService;
import cn.source.system.service.ISysUserService;
import cn.source.system.service.IUserMessageService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户消息Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class UserMessageImpl extends ServiceImpl<UserMessageMapper, UserMessage> implements IUserMessageService {

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysUserCategoryService userCategoryService;

    @Value("${wechat.mini.appId}")
    private String APPID;

    @Value("${wechat.mini.secret}")
    private String SECRET;

    @Override
    public void sendSubscribeMsg(Long msgId) throws WxErrorException {
        UserMessage userMessage = baseMapper.selectById(msgId);
        SysUser publishUser = userService.selectUserById(userMessage.getPublishId());
        List<Long> userIdList = userCategoryService.list(new LambdaQueryWrapper<SysUserCategory>()
                .eq(SysUserCategory::getCId, userMessage.getCategoryId()))
                .stream()
                .map(SysUserCategory::getUserId)
                .collect(Collectors.toList());
        if (userIdList.size() > 0) {
            WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
            config.setAppid(APPID);
            config.setSecret(SECRET);
            WxMaService service = new WxMaServiceImpl();
            service.setWxMaConfig(config);
            for (Long userId : userIdList) {
                SysUser sysUser = userService.selectUserById(userId);
                if (StringUtils.isNotBlank(sysUser.getOpenId())) {
                    WxMaSubscribeMessage wxMaSubscribeMessage = new WxMaSubscribeMessage();
                    //跳转小程序页面路径
                    wxMaSubscribeMessage.setPage("pages/index/index");
                    //模板消息id
                    wxMaSubscribeMessage.setTemplateId("xxAsj5FHGgVjGAPNCVqDl9IPLnKHRPNdSP5zO_clYkY");
//        wxMaSubscribeMessage.setTemplateId("Ewyd1EgXcxSOyqeuzYy6kAP0oEIFhf6yqpf48QMiHNQ");
                    //给谁推送 用户的openid （可以调用根据code换openid接口)
                    wxMaSubscribeMessage.setToUser(sysUser.getOpenId());
                    //==========================================创建一个参数集合========================================================
                    List<WxMaSubscribeMessage.Data> data = new ArrayList<>();
                    WxMaSubscribeMessage.Data thing2Data = new WxMaSubscribeMessage.Data();
                    thing2Data.setName("thing2");
                    thing2Data.setValue("您有一条居民消息待处理");
                    data.add(thing2Data);
                    WxMaSubscribeMessage.Data thing4Data = new WxMaSubscribeMessage.Data();
                    thing4Data.setName("thing4");
                    thing4Data.setValue(publishUser.getRealname());
                    data.add(thing4Data);
                    WxMaSubscribeMessage.Data time3Data = new WxMaSubscribeMessage.Data();
                    time3Data.setName("time3");
                    time3Data.setValue(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm"));
                    data.add(time3Data);
                    wxMaSubscribeMessage.setData(data);
                    WxMaMsgService msgService = service.getMsgService();
                    msgService.sendSubscribeMsg(wxMaSubscribeMessage);
                }

            }
        }
    }
}

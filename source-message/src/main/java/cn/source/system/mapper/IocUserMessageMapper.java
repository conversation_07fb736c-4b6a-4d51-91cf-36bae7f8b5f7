package cn.source.system.mapper;

import cn.source.system.domain.UserMessage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IocUserMessageMapper extends BaseMapper<UserMessage> {

    // 自定义接口
    @Select("SELECT \n" +
            "\ta.type as type, \n" +
            "\tCOUNT(a.id) as count \n" +
            "FROM\n" +
            "\tuser_message a\n" +
            "\tINNER JOIN sys_user b ON a.publish_id = b.user_id\n" +
            "\tINNER JOIN region c ON b.house_id = c.id\n" +
            "WHERE\n" +
            "\tc.parent_code = #{code}\n" +
            "GROUP BY\n" +
            "\ta.type\n" +
            "ORDER BY\n" +
            "\ta.type ASC")
    List<UserMessage> selectCountByGrid(String code);

    @Select("SELECT \n" +
            "\ta.type as type,\n" +
            "\ta.category_id as category_id, \n" +
            "\tCOUNT(a.id) as count \n" +
            "FROM\n" +
            "\tuser_message a\n" +
            "\tINNER JOIN sys_user b ON a.publish_id = b.user_id\n" +
            "\tINNER JOIN region c ON b.house_id = c.id\n" +
            "WHERE\n" +
            "\tc.parent_code = #{code}\n" +
            "GROUP BY\n" +
            "\ta.type, a.category_id\n" +
            "ORDER BY\n" +
            "\ta.type, a.category_id ASC")
    List<UserMessage> selectCategoryCountByGrid(String code);

    @Select("SELECT \n" +
            "\ta.type as type, \n" +
            "\tCOUNT(a.id) as count \n" +
            "FROM\n" +
            "\tuser_message a\n" +
            "\tINNER JOIN sys_user b ON a.publish_id = b.user_id\n" +
            "\tINNER JOIN region c ON b.house_id = c.id\n" +
            "WHERE\n" +
            "\tc.`code` = #{code}\n" +
            "GROUP BY\n" +
            "\ta.type\n" +
            "ORDER BY\n" +
            "\ta.type ASC")
    List<UserMessage> selectCountByHouse(String code);

    @Select("SELECT \n" +
            "\ta.type as type,\n" +
            "\ta.category_id as category_id, \n" +
            "\tCOUNT(a.id) as count \n" +
            "FROM\n" +
            "\tuser_message a\n" +
            "\tINNER JOIN sys_user b ON a.publish_id = b.user_id\n" +
            "\tINNER JOIN region c ON b.house_id = c.id\n" +
            "WHERE\n" +
            "\tc.`code` = #{code}\n" +
            "GROUP BY\n" +
            "\ta.type, a.category_id\n" +
            "ORDER BY\n" +
            "\ta.type, a.category_id ASC;")
    List<UserMessage> selectCategoryCountByHouse(String code);
}

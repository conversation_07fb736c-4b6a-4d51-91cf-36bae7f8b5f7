package cn.source.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 信息图片表
 */
@Data
@TableName("user_message_discuss_media")
public class UserMessageDiscussMedia {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    private Long discussId;

    private String mediaName;

    private String mediaPath;

    private String mediaUrl;
    private String mediaType;

    private BigDecimal mediaSize;

}

package cn.source.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 信息表
 *
 * <AUTHOR>
 * @date 2022-03-27
 */
@Data
@TableName("user_message")
public class UserMessage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 文章ID
     */
    @TableId
    private Long id;

    private Long categoryId;

    @TableField(exist = false)
    private String categoryName;

    private String createName;

    private Long publishId;

    @TableField(exist = false)
    private String publishName;

    @TableField(exist = false)
    private String publishAddress;

    @TableField(exist = false)
    private String publishPhone;

    private Integer type;

    private String messageTitle;

    private String messageContent;

    private String replyMessageContent;

    private String status;

    private String discussStatus;

    private String showStatus;

    private Long replyUserId;

    //星级评价
    private Integer rateValue;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date replyTime;
    @TableField(exist = false)
    private Integer searchDateType;

    @TableField(exist = false)
    private Boolean isXcx;

    @TableField(exist = false)
    private Integer count;
}

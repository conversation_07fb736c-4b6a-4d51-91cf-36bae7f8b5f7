package cn.source.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 评论表
 *
 * <AUTHOR>
 */
@Data
@TableName("user_message_discuss")
public class UserMessageDiscuss implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 评论ID
     */
    @TableId
    private Long id;

    private Long userId;

    @TableField(exist = false)
    private String userName;

    private Long replyId;

    private Long atId;

    private Long messageId;

    private String content;

    private Integer praiseNum;

    private Integer replyNum;

    @JsonFormat(pattern = "yy-MM-dd HH:mm")
    private Date createTime;
    @TableField(exist = false)
    private List<String> discussImageList;
    @TableField(exist = false)
    private List<String> discussVideoList;

}

package cn.source.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 小区网格表
 *
 * <AUTHOR>
 */
@Data
@TableName("region")
public class Region implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Integer id;
    /**
     * 名称
     */
    private String name;
    /**
     * 编码
     */
    private String code;

    /**
     * 父级编码
     */
    private String parentCode;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 类型0网格1小区
     */
    private String type;

    /**
     * 备注
     */
    private String remark;

    /**
     * 楼栋
     */
    private String building;

    /**
     * 单元
     */
    private String unit;

}

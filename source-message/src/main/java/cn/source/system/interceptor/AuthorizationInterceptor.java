package cn.source.system.interceptor;

import cn.source.common.constant.Constants;
import cn.source.common.exception.ServiceException;
import cn.source.common.utils.StringUtils;
import cn.source.framework.web.service.TokenService;
import cn.source.system.annotation.Login;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * token验证
 */
@Component
public class AuthorizationInterceptor extends HandlerInterceptorAdapter {
    @Resource
    private TokenService tokenService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Login annotation;
        if (handler instanceof HandlerMethod) {
            annotation = ((HandlerMethod) handler).getMethodAnnotation(Login.class);
        } else {
            return true;
        }

        if (annotation == null) {
            return true;
        }

        //获取用户凭证
        String token = tokenService.getToken(request);

        //凭证为空
        if (StringUtils.isBlank(token)) {
            throw new ServiceException(token + "不能为空", HttpStatus.UNAUTHORIZED.value());
        }

        if (tokenService.isExpiration(token)) {
            throw new ServiceException(token + "失效，请重新登录", HttpStatus.UNAUTHORIZED.value());
        }

        //设置userId到request里，后续根据userId，获取用户信息
        request.setAttribute(Constants.LOGIN_USER_KEY, tokenService.getLoginUser(request).getUserId());

        return true;
    }
}

package cn.source.system.controller;

import cn.source.common.core.controller.BaseController;
import cn.source.common.core.domain.AjaxResult;
import cn.source.common.core.page.TableDataInfo;
import cn.source.common.utils.StringUtils;
import cn.source.system.annotation.Login;
import cn.source.system.domain.UserMessage;
import cn.source.system.domain.UserMessageDiscuss;
import cn.source.system.service.IUserMessageDiscussService;
import cn.source.system.service.IUserMessageService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 信息管理Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/userMessage/message")
public class UserMessageController extends BaseController {
    @Resource
    private IUserMessageService userMessageService;
    @Resource
    private IUserMessageDiscussService userMessageDiscussService;

    /**
     * 查询用户信息列表
     */
    @PreAuthorize("@ss.hasPermi('userMessage:message:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserMessage userMessage) {
        LambdaQueryWrapper<UserMessage> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(userMessage.getStatus())) {
            queryWrapper.eq(UserMessage::getStatus, userMessage.getStatus());
        }
        if (userMessage.getType() != null) {
            queryWrapper.eq(UserMessage::getType, userMessage.getType());
        }
        startPage();
        List<UserMessage> list = userMessageService.list(queryWrapper);
        return getDataTable(list);
    }

    /**
     * 根据通知公告编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('userMessage:message:query')")
    @GetMapping(value = "/{messageId}")
    public AjaxResult getInfo(@PathVariable Long messageId) {
        return AjaxResult.success(userMessageService.getById(messageId));
    }

    /**
     * 修改用户信息
     */
    @PutMapping("")
    public AjaxResult edit(@RequestBody UserMessage userMessage) {
        return toAjax(userMessageService.updateById(userMessage));
    }
    /**
     * 查询信息评价列表
     */
    @PreAuthorize("@ss.hasPermi('userMessage:discuss:list')")
    @GetMapping("/discuss/{messageId}")
    public TableDataInfo discussList(@PathVariable Long messageId) {
        LambdaQueryWrapper<UserMessageDiscuss> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserMessageDiscuss::getMessageId, messageId);
        startPage();
        List<UserMessageDiscuss> list = userMessageDiscussService.list(queryWrapper);
        return getDataTable(list);
    }

    /**
     * 删除用户信息
     */
    @DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPermi('userMessage:discuss:list')")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(userMessageService.removeByIds(Arrays.asList(ids)));
    }
}

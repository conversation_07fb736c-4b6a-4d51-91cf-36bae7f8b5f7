package cn.source.system.controller;

import cn.source.common.core.controller.BaseController;
import cn.source.common.core.domain.AjaxResult;
import cn.source.common.utils.StringUtils;
import cn.source.system.domain.Region;
import cn.source.system.service.IRegionService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 居民小程序登录
 */
@RequestMapping("/api/region")
@RestController
public class RegionApiController extends BaseController {
    @Resource
    private IRegionService regionService;

    @GetMapping("/list")
    public AjaxResult list(Region region) {
        LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(region.getType())) {
            queryWrapper.eq(Region::getType, region.getType());
        }
        List<Region> list = regionService.list(queryWrapper);
        List<Map<String, Object>> ret = new ArrayList<>(list.size());
        for (Region item : list) {
            Map<String, Object> dicMap = new HashMap();
            dicMap.put("value", item.getId());
            dicMap.put("label", item.getName());
            ret.add(dicMap);
        }
        return AjaxResult.success(ret);
    }
}

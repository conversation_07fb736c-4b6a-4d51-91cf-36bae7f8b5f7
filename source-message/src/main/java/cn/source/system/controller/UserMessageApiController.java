package cn.source.system.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.source.common.core.controller.BaseController;
import cn.source.common.core.domain.AjaxResult;
import cn.source.common.core.domain.entity.SysDictData;
import cn.source.common.core.domain.entity.SysUser;
import cn.source.common.core.page.TableDataInfo;
import cn.source.common.utils.StringUtils;
import cn.source.common.utils.bean.BeanUtils;
import cn.source.system.annotation.Login;
import cn.source.system.annotation.LoginUser;
import cn.source.system.domain.*;
import cn.source.system.form.UserMessageForm;
import cn.source.system.service.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 信息管理Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/userMessage")
public class UserMessageApiController extends BaseController {
    @Resource
    private IUserMessageService userMessageService;
    @Resource
    private IUserMessageDiscussService userMessageDiscussService;
    @Resource
    private ISysDictDataService sysDictDataService;
    @Resource
    private IUserMessageImageService userMessageImageService;
    @Resource
    private ISysUserCategoryService userCategoryService;
    @Resource
    private ISysDictDataService dictDataService;
    @Resource
    private ISysUserService userService;
    @Resource
    private IUserMessageDiscussMediaService iUserMessageDiscussMediaService;

    /**
     * 查询用户小区列表
     */
    @Login
    @GetMapping("/houseList")
    public AjaxResult houseList() {
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("sys_user_house");
        sysDictData.setStatus("0");
        List<SysDictData> list = sysDictDataService.selectDictDataList(sysDictData);
        List<Map<String, Object>> ret = new ArrayList<>(list.size());
        for (SysDictData dictData : list) {
            Map<String, Object> dicMap = new HashMap();
            dicMap.put("value", dictData.getDictValue());
            dicMap.put("label", dictData.getDictLabel());
            ret.add(dicMap);
        }
        return AjaxResult.success(ret);
    }

    /**
     * 查询信息分类列表
     */
    @Login
    @GetMapping("/categoryList")
    public AjaxResult categoryList() {
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("sys_message_category");
        sysDictData.setStatus("0");
        List<SysDictData> list = sysDictDataService.selectDictDataList(sysDictData);
        List<Map<String, Object>> ret = new ArrayList<>(list.size());
        for (SysDictData dictData : list) {
            Map<String, Object> dicMap = new HashMap();
            dicMap.put("name", dictData.getDictValue());
            dicMap.put("label", dictData.getDictLabel());
            dicMap.put("icon", dictData.getRemark());
            ret.add(dicMap);
        }
        return AjaxResult.success(ret);
    }

    /**
     * 查询用户信息列表
     */
    @Login
    @GetMapping("/list")
    public TableDataInfo list(UserMessage userMessage) {
        LambdaQueryWrapper<UserMessage> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(userMessage.getStatus())) {
            queryWrapper.eq(UserMessage::getStatus, userMessage.getStatus());
        }
        if (userMessage.getType() != null) {
            queryWrapper.eq(UserMessage::getType, userMessage.getType());
        }
//        if (userMessage.getReplyUserId() != null) {
//            List<Long> categoryList = userCategoryService
//                    .list(new LambdaQueryWrapper<SysUserCategory>().eq(SysUserCategory::getUserId, userMessage.getReplyUserId()))
//                    .stream().map(SysUserCategory::getCId).collect(Collectors.toList());
//            queryWrapper.in(UserMessage::getCategoryId, categoryList);
////            queryWrapper.eq(UserMessage::getReplyUserId, userMessage.getReplyUserId());
//        }
        // 根据手机号查询用户Id
        if (userMessage.getPublishId() != null && userMessage.getPublishId() != 46) {
            if (userMessage.getIsXcx()) {
                queryWrapper.and(wrapper -> wrapper.eq(UserMessage::getPublishId, userMessage.getPublishId())
                                        .or().eq(UserMessage::getShowStatus, 1));
            } else {
                queryWrapper.eq(UserMessage::getPublishId, userMessage.getPublishId());
            }
         }
//        if (userMessage.getCategoryId() != null) {
//            queryWrapper.eq(UserMessage::getCategoryId, userMessage.getCategoryId());
//        }
        int dateType = userMessage.getSearchDateType() != null ? userMessage.getSearchDateType() : 0;
        LocalDateTime localDateTime = LocalDateTime.now().withNano(0).withSecond(0).withMinute(0).withHour(0);
        switch (dateType) {
            case 1:
                LocalDateTime[] localDateTimes = {localDateTime, localDateTime.withHour(23).withMinute(59)};
                queryWrapper.ge(UserMessage::getCreateTime, localDateTimes[0]).le(UserMessage::getCreateTime, localDateTimes[1]);
                break;
            case 2:
                LocalDateTime weeklocalDateTime = localDateTime.minusDays(localDateTime.getDayOfWeek().getValue());
                LocalDateTime[] weekLocalDateTimes = {weeklocalDateTime, localDateTime.withHour(23).withMinute(59)};
                queryWrapper.ge(UserMessage::getCreateTime, weekLocalDateTimes[0]).le(UserMessage::getCreateTime, weekLocalDateTimes[1]);
                break;
            case 3:
                LocalDateTime monthLocalDateTime = localDateTime.minusDays(localDateTime.getDayOfMonth());
                LocalDateTime[] monthLocalDateTimes = {monthLocalDateTime, localDateTime.withHour(23).withMinute(59)};
                queryWrapper.ge(UserMessage::getCreateTime, monthLocalDateTimes[0]).le(UserMessage::getCreateTime, monthLocalDateTimes[1]);
                break;
            case 4:
                break;
            default:
                break;
        }
//        if (userMessage.getIsXcx()) {
//            queryWrapper.or().eq(UserMessage::getShowStatus, 1);
//        }
        startPage();
        List<UserMessage> list = userMessageService.list(queryWrapper);
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("sys_message_category");
        List<SysDictData> dictDataList = dictDataService.selectDictDataList(sysDictData);
        List<UserMessage> ret = list.stream().map(message -> {
            SysUser sysUser = userService.selectUserById(message.getPublishId());
            String categoryName = dictDataList.stream().filter(item -> message.getCategoryId().equals(Convert.toLong(item.getDictValue()))).map(item -> item.getDictLabel()).collect(Collectors.toList()).get(0);
            message.setCategoryName(categoryName);
            message.setPublishName(sysUser.getRealname());
            message.setPublishPhone(sysUser.getPhonenumber());
            message.setPublishAddress(sysUser.getHouse() + "(" + sysUser.getRoom() + ")");
            return message;
        }).collect(Collectors.toList());

        return getDataTable(ret);
    }

    /**
     * 获取用户信息详细信息
     */
    @Login
    @GetMapping(value = "/findMessageById")
    public AjaxResult getInfo(@RequestParam Long id) {
        List<UserMessageDiscuss> userMessageDiscuss = userMessageDiscussService.list(new LambdaQueryWrapper<UserMessageDiscuss>().eq(UserMessageDiscuss::getMessageId, id));
        for (UserMessageDiscuss messageDiscuss : userMessageDiscuss) {
            String userName = userService.selectUserById(messageDiscuss.getUserId()).getRealname();
            messageDiscuss.setUserName(userName);
            List<UserMessageDiscussMedia> discussMedia = iUserMessageDiscussMediaService.list(new LambdaQueryWrapper<UserMessageDiscussMedia>()
                    .eq(UserMessageDiscussMedia::getDiscussId, messageDiscuss.getId()));
            List<String> imageList = new ArrayList<>();
            List<String> videoList = new ArrayList<>();
            for (UserMessageDiscussMedia messageDiscussMedia : discussMedia) {
                if ("image".equals(messageDiscussMedia.getMediaType())) {
                    imageList.add(messageDiscussMedia.getMediaUrl());
                }
                if ("video".equals(messageDiscussMedia.getMediaType())) {
                    videoList.add(messageDiscussMedia.getMediaUrl());
                }
            }
            messageDiscuss.setDiscussImageList(imageList);
            messageDiscuss.setDiscussVideoList(videoList);
        }
        UserMessage userMessage = userMessageService.getById(id);
        List<UserMessageMedia> imageList = userMessageImageService.list(new LambdaQueryWrapper<UserMessageMedia>().eq(UserMessageMedia::getMessageId, id));
        Map<String, Object> ret = new HashMap<>();
        ret.put("message", userMessage);
        if (userMessageDiscuss.size() > 0) {
            ret.put("discuss", userMessageDiscuss);
        } else {
            ret.put("discuss", null);
        }
        ret.put("imageList", imageList);

        return AjaxResult.success(ret);
    }

    /**
     * 新增用户信息
     */
    @Login
    @PostMapping("/saveMessage")
    @Transactional
    public AjaxResult add(@RequestBody UserMessageForm form, @LoginUser AppUser appUser) {
        UserMessage userMessage = new UserMessage();
        BeanUtils.copyBeanProp(userMessage, form);
        userMessage.setCreateTime(new Date());
        userMessageService.save(userMessage);
        List<UserMessageMedia> imageList = BeanUtil.copyToList(form.getMediaList(), UserMessageMedia.class);
        List<UserMessageMedia> images = imageList.stream().map(item -> {
            item.setMessageId(userMessage.getId());
            return item;
        }).collect(Collectors.toList());
        userMessageImageService.saveBatch(images);
        try {
            userMessageService.sendSubscribeMsg(userMessage.getId());
        } catch (WxErrorException exception) {
            logger.error(exception.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 修改用户信息
     */
    @Login
    @PostMapping("/updateMessage")
    public AjaxResult edit(@RequestBody UserMessage userMessage) {
        if (StringUtils.isNotEmpty(userMessage.getReplyMessageContent())) {
            userMessage.setReplyTime(new Date());
            userMessage.setStatus("1");
        }
        return toAjax(userMessageService.updateById(userMessage));
    }

    /**
     * 删除用户信息
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(userMessageService.removeByIds(Arrays.asList(ids)));
    }
}

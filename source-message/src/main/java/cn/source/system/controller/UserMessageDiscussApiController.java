package cn.source.system.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.source.common.core.controller.BaseController;
import cn.source.common.core.domain.AjaxResult;
import cn.source.common.core.page.TableDataInfo;
import cn.source.common.utils.bean.BeanUtils;
import cn.source.common.validator.ValidatorUtils;
import cn.source.system.domain.UserMessage;
import cn.source.system.domain.UserMessageDiscuss;
import cn.source.system.domain.UserMessageDiscussMedia;
import cn.source.system.form.UserMessageDiscussForm;
import cn.source.system.service.IUserMessageDiscussMediaService;
import cn.source.system.service.IUserMessageDiscussService;
import cn.source.system.service.IUserMessageService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 信息评论Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/userMessageDiscuss")
public class UserMessageDiscussApiController extends BaseController {
    @Resource
    private IUserMessageDiscussService iUserMessageDiscussService;
    @Resource
    private IUserMessageService iUserMessageService;
    @Resource
    private IUserMessageDiscussMediaService iUserMessageDiscussMediaService;

    /**
     * 查询信息评论列表
     */
    @GetMapping("/list/{id}")
    public TableDataInfo list(@PathVariable Long id) {
        List<UserMessageDiscuss> list = iUserMessageDiscussService.list(new LambdaQueryWrapper<UserMessageDiscuss>().eq(UserMessageDiscuss::getMessageId, id));
        List<UserMessageDiscuss> ret = list.stream().map(item -> {
            List<UserMessageDiscussMedia> discussMedia = iUserMessageDiscussMediaService.list(new LambdaQueryWrapper<UserMessageDiscussMedia>()
                    .eq(UserMessageDiscussMedia::getDiscussId, item.getId()));
            return item;
        }).collect(Collectors.toList());

        return getDataTable(ret);
    }

    /**
     * 新增信息评论
     */
    @PostMapping
    @Transactional
    public AjaxResult add(@RequestBody UserMessageDiscussForm form) {
        ValidatorUtils.validateEntity(form);
        UserMessage userMessage = iUserMessageService.getById(form.getMessageId());
//        if ("1".equals(userMessage.getDiscussStatus())) {
//            return AjaxResult.error("不可重复评价");
//        }
        UserMessageDiscuss messageDiscuss = new UserMessageDiscuss();
        BeanUtils.copyBeanProp(messageDiscuss, form);
        messageDiscuss.setCreateTime(new Date());


        if (ObjectUtils.isEmpty(userMessage)) {
            return AjaxResult.error("系统繁忙");
        }
        if (ObjectUtils.isNotEmpty(form.getUserId())) {
            userMessage.setDiscussStatus("1");
            userMessage.setRateValue(form.getRateValue());
        }
        if (ObjectUtils.isNotEmpty(form.getReplyUserId())) {
            userMessage.setReplyTime(new Date());
            userMessage.setStatus("1");
            //增加回复人ID
            userMessage.setReplyUserId(form.getReplyUserId());
            messageDiscuss.setUserId(form.getReplyUserId());
        }

        iUserMessageDiscussService.save(messageDiscuss);
        iUserMessageService.updateById(userMessage);
        if (form.getMediaList().size() > 0) {
            List<UserMessageDiscussMedia> imageList = BeanUtil.copyToList(form.getMediaList(), UserMessageDiscussMedia.class);
            List<UserMessageDiscussMedia> images = imageList.stream().map(item -> {
                item.setDiscussId(messageDiscuss.getId());
                return item;
            }).collect(Collectors.toList());
            iUserMessageDiscussMediaService.saveBatch(images);
        }
        return AjaxResult.success();
    }

    /**
     * 删除信息评论
     */
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(iUserMessageDiscussService.removeById(id));
    }
}

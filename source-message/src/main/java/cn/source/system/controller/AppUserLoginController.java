package cn.source.system.controller;

import cn.source.common.constant.Constants;
import cn.source.common.core.controller.BaseController;
import cn.source.common.core.domain.AjaxResult;
import cn.source.common.core.domain.model.LoginBody;
import cn.source.common.core.domain.model.LoginUser;
import cn.source.common.core.redis.RedisCache;
import cn.source.framework.web.service.TokenService;
import cn.source.system.domain.AppUser;
import cn.source.system.service.IAppUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 居民小程序登录
 */
@RequestMapping("/api")
@RestController
public class AppUserLoginController extends BaseController {
    @Resource
    private IAppUserService appUserService;
    @Resource
    private RedisCache redisCache;
    @Resource
    private TokenService tokenService;

    /**
     * 手机注册/登录
     */
    @PostMapping("/appLogin")
    public AjaxResult appLogin(HttpServletRequest request,
                                    @RequestBody LoginBody loginBody) {
        AjaxResult ajax = AjaxResult.success();
        String msg = "登录成功";
        // 首先验证验证码是否正确
        if (redisCache.getCacheObject(loginBody.getUsername()) == null || !redisCache.getCacheObject(loginBody.getUsername()).equals(loginBody.getCode())) {
            msg = "验证码过期/错误";
            return error(msg);
        }
        // 验证码正确则判断是否为新用户
        AppUser appUser = appUserService.getOne(new LambdaQueryWrapper<AppUser>().eq(AppUser::getUserName, loginBody.getUsername()));
        // 不是新用户，创建用户
        if (appUser == null) {
            appUser = new AppUser();
            appUser.setUserName(loginBody.getUsername());
            appUser.setNickName(loginBody.getUsername());
            appUser.setPhonenumber(loginBody.getUsername());
            appUserService.save(appUser);
        }
        // 生成token
        LoginUser loginUser = new LoginUser(appUser.getUserId());
        String token = tokenService.createToken(loginUser);
        ajax.put(Constants.TOKEN, token);
        ajax.put("loginUser", loginUser);
        return ajax;
    }
}

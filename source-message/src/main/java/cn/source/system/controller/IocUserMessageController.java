package cn.source.system.controller;

import cn.source.common.core.controller.BaseController;
import cn.source.common.utils.DictUtils;
import cn.source.common.utils.StringUtils;
import cn.source.system.domain.UserMessage;
import cn.source.system.mapper.IocUserMessageMapper;
import cn.source.system.service.IIocUserMessageService;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/ioc/userMessage")
public class IocUserMessageController extends BaseController {

    @Autowired
    private IIocUserMessageService iocUserMessageService;

    @Autowired
    private IocUserMessageMapper iocUserMessageMapper;

    /**
     * ioc大屏--民意互动情况统计数据
     */
    @GetMapping("/count")
    public Object getIocUserMessageCount(String type, String code) {
        List<UserMessage> totalCountList = null;
        List<UserMessage> categoryCountList = null;
        if ("1".equals(type)) {
            // 社区界面
            QueryWrapper<UserMessage> queryWrapper = new QueryWrapper<>();
            queryWrapper
                    .select("type, count(*) as count")
                    .groupBy("type")
                    .orderByAsc("type");
            totalCountList = iocUserMessageMapper.selectList(queryWrapper);

            queryWrapper
                    .select("type, category_id, COUNT(*) as count")
                    .groupBy("type, category_id")
                    .orderByAsc("type, category_id");
            categoryCountList = iocUserMessageMapper.selectList(queryWrapper);
        }
        if ("2".equals(type)) {
            // 网格界面
            totalCountList = iocUserMessageMapper.selectCountByGrid(code);
            categoryCountList = iocUserMessageMapper.selectCategoryCountByGrid(code);
        }
        if ("3".equals(type)) {
            // 小区界面
            totalCountList = iocUserMessageMapper.selectCountByHouse(code);
            categoryCountList = iocUserMessageMapper.selectCategoryCountByHouse(code);
        }

        Map<String, Object> resultMap = new LinkedHashMap<>();

        if (StringUtils.isNotEmpty(totalCountList)) {
            for (UserMessage userMessage : totalCountList) {
                Integer messageType = userMessage.getType();
                Integer count = userMessage.getCount();
                if (messageType == 1) {
                    resultMap.put("ziXunNum", count);
                }
                if (messageType == 2) {
                    resultMap.put("suQiuNum", count);
                }
                if (messageType == 3) {
                    resultMap.put("jianYiNum", count);
                }
                if (messageType == 4) {
                    resultMap.put("biaoYangNum", count);
                }
            }
        } else {
            resultMap.put("ziXunNum", 0);
            resultMap.put("suQiuNum", 0);
            resultMap.put("jianYiNum", 0);
            resultMap.put("biaoYangNum", 0);
        }

        Integer[] categoryIds = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 99};
        Map<Integer, Integer> ziXunCountMap = new LinkedHashMap<>();
        Map<Integer, Integer> suQiuCountMap = new LinkedHashMap<>();
        Map<Integer, Integer> jianYiCountMap = new LinkedHashMap<>();
        Map<Integer, Integer> biaoYangCountMap = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(categoryCountList)) {
            for (UserMessage userMessage : categoryCountList) {
                Integer messageType = userMessage.getType();
                Integer categoryId = userMessage.getCategoryId().intValue();
                Integer count = userMessage.getCount();
                if (messageType == 1) {
                    ziXunCountMap.put(categoryId, count);
                }
                if (messageType == 2) {
                    suQiuCountMap.put(categoryId, count);
                }
                if (messageType == 3) {
                    jianYiCountMap.put(categoryId, count);
                }
                if (messageType == 4) {
                    biaoYangCountMap.put(categoryId, count);
                }
            }
        }

        for (Integer integer : categoryIds) {
            if (!ziXunCountMap.containsKey(integer)) {
                ziXunCountMap.put(integer, 0);
            }
            if (!suQiuCountMap.containsKey(integer)) {
                suQiuCountMap.put(integer, 0);
            }
            if (!jianYiCountMap.containsKey(integer)) {
                jianYiCountMap.put(integer, 0);
            }
            if (!biaoYangCountMap.containsKey(integer)) {
                biaoYangCountMap.put(integer, 0);
            }
        }

        List<Integer> ziXunCounts = new ArrayList<>(ziXunCountMap.values());
        List<Integer> suQiuCounts = new ArrayList<>(suQiuCountMap.values());
        List<Integer> jianYiCounts = new ArrayList<>(jianYiCountMap.values());
        List<Integer> biaoYangCounts = new ArrayList<>(biaoYangCountMap.values());

        resultMap.put("ziXunCount", ziXunCounts);
        resultMap.put("suQiuCount", suQiuCounts);
        resultMap.put("jianYiCount", jianYiCounts);
        resultMap.put("biaoYangCount", biaoYangCounts);

        return resultMap;
    }
}

package cn.source.system.form;

import cn.source.system.domain.UserMessageDiscussMedia;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 评论表
 *
 * <AUTHOR>
 */
@Data
public class UserMessageDiscussForm implements Serializable {
    private static final long serialVersionUID = 1L;

    //    @NotNull(message = "userId不能为空")
    private Long userId;

    private Long replyUserId;

    private Long replyId;

    private Long atId;

    @NotNull(message = "messageId不能为空")
    private Long messageId;
    //    @NotBlank(message = "评论内容不能为空")
//    @Size(min = 1, max = 100, message = "请检查评论内容长度")
    private String content;

    @Min(value = 1, message = "评价至少1星")
    private Integer rateValue;

    private List<UserMessageDiscussMedia> mediaList;

}

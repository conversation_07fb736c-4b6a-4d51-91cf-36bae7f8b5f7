package cn.source.system.form;

import cn.source.system.domain.UserMessageMedia;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 评论表
 *
 * <AUTHOR>
 */
@Data
public class UserMessageForm implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long categoryId;

    private String createName;
    @NotNull(message = "publishId不能为空")
    private Long publishId;

    private Integer type;

    private String messageTitle;
    @NotNull(message = "messageContent不能为空")
    @Size(min = 1, max = 500, message = "请检查评论内容长度")
    private String messageContent;

    private String replyMessageContent;

    private Long replyUserId;

    private List<UserMessageMedia> mediaList;
}

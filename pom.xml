<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.source</groupId>
    <artifactId>source</artifactId>
    <version>3.8.1</version>

    <name>source</name>
    <url>https://sourcebyte.vip</url>
    <description>开源字节</description>

    <properties>
        <source.version>3.8.1</source.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <druid.version>1.2.8</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <swagger.version>3.0.0</swagger.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <mybatis-spring-boot.version>2.2.0</mybatis-spring-boot.version>
        <mybatis-plus-spring-boot.version>3.3.2</mybatis-plus-spring-boot.version>
        <pagehelper.boot.version>1.4.0</pagehelper.boot.version>
        <fastjson.version>1.2.83</fastjson.version>
        <oshi.version>5.8.6</oshi.version>
        <jna.version>5.10.0</jna.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.fileupload.version>1.4</commons.fileupload.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <poi.version>4.1.2</poi.version>
        <velocity.version>2.3</velocity.version>
        <log4j2.version>2.17.1</log4j2.version>
        <jwt.version>0.9.1</jwt.version>
        <flowable.version>6.7.2</flowable.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.5.8</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>2.5.8</version>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

<!--            &lt;!&ndash; SpringBoot集成mybatis框架 &ndash;&gt;-->
<!--            <dependency>-->
<!--                <groupId>org.mybatis.spring.boot</groupId>-->
<!--                <artifactId>mybatis-spring-boot-starter</artifactId>-->
<!--                <version>${mybatis-spring-boot.version}</version>-->
<!--            </dependency>-->
            <!--MyBatis Plus-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-spring-boot.version}</version>
            </dependency>
            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>
            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna-platform</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <!-- Swagger3依赖 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- 文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- collections工具类 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- log4j日志组件 -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j2.version}</version>
            </dependency>

            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>cn.source</groupId>
                <artifactId>source-quartz</artifactId>
                <version>${source.version}</version>
            </dependency>

            <!-- flowable -->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${flowable.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>cn.source</groupId>
                <artifactId>source-generator</artifactId>
                <version>${source.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>cn.source</groupId>
                <artifactId>source-framework</artifactId>
                <version>${source.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>cn.source</groupId>
                <artifactId>source-system</artifactId>
                <version>${source.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>cn.source</groupId>
                <artifactId>source-common</artifactId>
                <version>${source.version}</version>
            </dependency>

            <!-- CMS模块-->
            <dependency>
                <groupId>cn.source</groupId>
                <artifactId>source-cms</artifactId>
                <version>${source.version}</version>
            </dependency>

            <!-- house模块-->
            <dependency>
                <groupId>cn.source</groupId>
                <artifactId>source-house</artifactId>
                <version>${source.version}</version>
            </dependency>

            <!-- im模块-->
            <dependency>
                <groupId>cn.source</groupId>
                <artifactId>source-im</artifactId>
                <version>${source.version}</version>
            </dependency>

            <!-- 商城模块-->
            <dependency>
                <groupId>cn.source</groupId>
                <artifactId>source-mall</artifactId>
                <version>${source.version}</version>
            </dependency>

            <!-- flow模块-->
            <dependency>
                <groupId>cn.source</groupId>
                <artifactId>source-flowable</artifactId>
                <version>${source.version}</version>
            </dependency>

            <!--微信公众号开发需要引入的依赖-->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>3.1.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>source-admin</module>
        <module>source-framework</module>
        <module>source-system</module>
        <module>source-quartz</module>
        <module>source-generator</module>
        <module>source-common</module>
        <!--新增CMS模块-->
        <module>source-cms</module>
        <!--新增租房模块-->
        <module>source-house</module>
        <!--新增IM模块-->
        <module>source-im</module>
        <!--新增mall模块-->
        <module>source-mall</module>
        <!--新增工作流模块-->
        <module>source-flowable</module>
        <module>source-message</module>
    </modules>
    <packaging>pom</packaging>


    <dependencies>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>

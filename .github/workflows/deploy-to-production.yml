# 文件路径: .github/workflows/deploy-to-production.yml

# 工作流的名称，将显示在GitHub的Actions标签页中
name: Deploy Java API to Production

# 触发条件：当有代码推送到'main'分支时触发
on:
  push:
    branches:
      - main

# 定义工作流中的作业
jobs:
  # 作业ID，可自定义
  build-and-deploy:
    # 指定作业运行的Runner环境
    runs-on: ubuntu-latest

    # 作业中包含的步骤，将按顺序执行
    steps:
      # 步骤1：检出代码
      # 使用官方的actions/checkout@v4，将仓库代码下载到Runner中
      - name: Checkout Code
        uses: actions/checkout@v4

      # 步骤2：设置Java环境
      # 使用官方的actions/setup-java@v4，安装指定版本的Java
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: '8'
          distribution: 'temurin'  # Eclipse Temurin (OpenJDK)
          cache: 'maven'           # 启用对Maven依赖的缓存，加速后续构建

      # 步骤3：运行测试（如果存在测试则运行，否则跳过）
      - name: Run Tests (if present)
        run: |
          if mvn help:describe -Dplugin=org.apache.maven.plugins:maven-surefire-plugin -Ddetail=false -q 2>/dev/null; then
            mvn test
          else
            echo "No test configuration found, skipping tests."
          fi

      # 步骤4：构建应用
      # 使用Maven构建整个项目，生成可执行的JAR文件
      - name: Build Application
        run: |
          mvn clean package -DskipTests
          # 验证构建产物是否存在
          if [ ! -f "source-admin/target/source-admin.jar" ]; then
            echo "Build failed: source-admin.jar not found"
            exit 1
          fi
          echo "Build successful: source-admin.jar created"

      # 步骤5：停止服务器上的现有服务
      - name: Stop existing service on server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail

            # 使用您提供的停止脚本
            echo "Stopping existing Java service..."
            PORT=12345
            ps -ef | grep java | grep source-admin | grep -- "--server.port=${PORT}" | grep -v grep | awk '{print $2}' | xargs -r kill -9
            echo "应用已停止"

            # 等待进程完全停止
            sleep 3

      # 步骤6：准备服务器目录并上传JAR文件
      - name: Prepare target directory and upload JAR
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.mapi"
            mkdir -p "$APP_DIR"
            # 备份现有的JAR文件（如果存在）
            if [ -f "$APP_DIR/source-admin.jar" ]; then
              mv "$APP_DIR/source-admin.jar" "$APP_DIR/source-admin.jar.backup.$(date +%Y%m%d_%H%M%S)"
              echo "Existing JAR backed up"
            fi

      - name: Upload JAR file via SCP
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "source-admin/target/source-admin.jar"
          target: "/www/zn/zn.mapi/"
          overwrite: true
          strip_components: 2  # 移除 source-admin/target/ 路径前缀

      # 步骤7：设置权限并启动服务
      - name: Set permissions and start service
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.mapi"
            
            # 设置文件权限
            chmod 755 "$APP_DIR/source-admin.jar"
            
            # 进入应用目录
            cd "$APP_DIR"
            
            # 启动服务（使用您提供的启动脚本）
            echo "Starting Java service..."
            nohup java -jar -Duser.timezone=Asia/Shanghai -Xms32M -Xmx64M source-admin.jar --server.port=12345 > mapi.log 2>&1 &
            
            # 等待服务启动
            sleep 10
            
            # 验证服务是否启动成功
            PORT=12345
            PID=$(ps -ef | grep java | grep source-admin | grep -- "--server.port=${PORT}" | grep -v grep | awk '{print $2}' || true)
            if [ ! -z "$PID" ]; then
              echo "Service started successfully with PID: $PID"
              echo "Service is running on port ${PORT}"

              # 可选：检查端口是否监听
              if command -v netstat >/dev/null 2>&1; then
                netstat -tlnp | grep :${PORT} || echo "Port ${PORT} not yet listening, service may still be starting..."
              fi
            else
              echo "Failed to start service"
              echo "Last 20 lines of log:"
              tail -20 mapi.log || echo "No log file found"
              exit 1
            fi
            
            echo "Deployment completed successfully!"

      # 步骤8：部署后验证（可选）
      - name: Post-deployment verification
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.mapi"
            
            # 等待服务完全启动
            echo "Waiting for service to fully start..."
            sleep 15
            
            # 检查服务状态
            PORT=12345
            PID=$(ps -ef | grep java | grep source-admin | grep -- "--server.port=${PORT}" | grep -v grep | awk '{print $2}' || true)
            if [ ! -z "$PID" ]; then
              echo "✅ Service is running with PID: $PID"
              
              # 显示最新的日志
              echo "📋 Latest application logs:"
              tail -10 "$APP_DIR/mapi.log" || echo "No recent logs available"
              
              # 可选：健康检查（如果您的应用有健康检查端点）
              # curl -f http://localhost:12345/health || echo "Health check endpoint not available"
              
            else
              echo "❌ Service verification failed - process not found"
              echo "📋 Full log content:"
              cat "$APP_DIR/mapi.log" || echo "No log file found"
              exit 1
            fi

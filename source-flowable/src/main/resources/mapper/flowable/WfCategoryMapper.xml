<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.source.flowable.mapper.WfCategoryMapper">
    
    <resultMap type="WfCategory" id="WfCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="code"    column="code"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectWfCategoryVo">
        select category_id, category_name, code, remark, create_by, create_time, update_by, update_time, del_flag from wf_category
    </sql>

    <select id="selectWfCategoryList" parameterType="WfCategory" resultMap="WfCategoryResult">
        <include refid="selectWfCategoryVo"/>
        <where>  
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
        </where>
    </select>
    
    <select id="selectWfCategoryByCategoryId" parameterType="Long" resultMap="WfCategoryResult">
        <include refid="selectWfCategoryVo"/>
        where category_id = #{categoryId}
    </select>
        
    <insert id="insertWfCategory" parameterType="WfCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into wf_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null">category_name,</if>
            <if test="code != null">code,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null">#{categoryName},</if>
            <if test="code != null">#{code},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateWfCategory" parameterType="WfCategory">
        update wf_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null">category_name = #{categoryName},</if>
            <if test="code != null">code = #{code},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteWfCategoryByCategoryId" parameterType="Long">
        delete from wf_category where category_id = #{categoryId}
    </delete>

    <delete id="deleteWfCategoryByCategoryIds" parameterType="String">
        delete from wf_category where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <select id="checkCategoryCodeUnique" parameterType="String" resultMap="WfCategoryResult">
        <include refid="selectWfCategoryVo"/>
        where code = #{code}
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.source.flowable.mapper.WfFormMapper">
    
    <resultMap type="WfForm" id="WfFormResult">
        <result property="formId"    column="form_id"    />
        <result property="formName"    column="form_name"    />
        <result property="content"    column="content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectWfFormVo">
        select form_id, form_name, content, create_by, create_time, update_by, update_time, remark, del_flag from wf_form
    </sql>

    <select id="selectWfFormList" parameterType="WfForm" resultMap="WfFormResult">
        <include refid="selectWfFormVo"/>
        <where>  
            <if test="formName != null  and formName != ''"> and form_name like concat('%', #{formName}, '%')</if>
        </where>
    </select>
    
    <select id="selectWfFormByFormId" parameterType="Long" resultMap="WfFormResult">
        <include refid="selectWfFormVo"/>
        where form_id = #{formId}
    </select>
        
    <insert id="insertWfForm" parameterType="WfForm" useGeneratedKeys="true" keyProperty="formId">
        insert into wf_form
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formName != null">form_name,</if>
            <if test="content != null">content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="formName != null">#{formName},</if>
            <if test="content != null">#{content},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateWfForm" parameterType="WfForm">
        update wf_form
        <trim prefix="SET" suffixOverrides=",">
            <if test="formName != null">form_name = #{formName},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where form_id = #{formId}
    </update>

    <delete id="deleteWfFormByFormId" parameterType="Long">
        delete from wf_form where form_id = #{formId}
    </delete>

    <delete id="deleteWfFormByFormIds" parameterType="String">
        delete from wf_form where form_id in 
        <foreach item="formId" collection="array" open="(" separator="," close=")">
            #{formId}
        </foreach>
    </delete>
</mapper>
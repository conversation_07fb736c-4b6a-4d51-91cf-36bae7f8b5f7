package cn.source.common.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.HttpMethod;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class AwsS3Utils implements InitializingBean {
    @Value("${aws.accessKey}")
    private String accessKey;
    @Value("${aws.secretKey}")
    private String accessSecret;
    @Value("${aws.bucket}")
    private String bucket;
    @Value("${aws.endpoint}")
    private String endpoint;
    private AmazonS3 client;

    @Override
    public void afterPropertiesSet() {
        ClientConfiguration config = new ClientConfiguration();
        config.setProtocol(Protocol.HTTP);
        config.disableSocketProxy();
        this.client = AmazonS3ClientBuilder
                .standard()
                .withClientConfiguration(config)
                .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials(accessKey, accessSecret)))
//                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint, Regions.CN_NORTH_1.getName()))
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint, "chengdu6"))
                .enablePathStyleAccess()
                .build();
    }

    /**
     * 执行文件上传
     *
     * @param file 要上传的文件的路径
     * @param key  存储文件的路径
     * @return 文件路径
     */
    public String upload(File file, String key) {
        client.putObject(new PutObjectRequest(bucket, key, file).withCannedAcl(CannedAccessControlList.PublicRead));
        GeneratePresignedUrlRequest urlRequest = new GeneratePresignedUrlRequest(bucket, key);
        URL url = client.generatePresignedUrl(urlRequest);
        return url.toString();
    }

    /**
     * 文件流执行文件上传
     * @param input
     * @param key
     * @return
     * @throws IOException
     */
    public String upload(InputStream input, String key) throws IOException {
        Date expireDate = new Date(System.currentTimeMillis() + TimeUnit.DAYS.toMillis(30));
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setHttpExpiresDate(expireDate);
        metadata.setContentLength(input.available());
        client.putObject(new PutObjectRequest(bucket, key, input, metadata).withCannedAcl(CannedAccessControlList.PublicRead));
        GeneratePresignedUrlRequest urlRequest = new GeneratePresignedUrlRequest(bucket, key);
        URL url = client.generatePresignedUrl(urlRequest);
        return url.toString();

    }

    public String generatePresignedUrl(String key) {
        // token设置1小时后过期
        Date expiration = DateUtil.offsetMinute(new Date(), 10);
        GeneratePresignedUrlRequest urlRequest = new GeneratePresignedUrlRequest(bucket, key)
                .withExpiration(expiration)
                .withMethod(HttpMethod.PUT);
        URL url = this.client.generatePresignedUrl(urlRequest);
        return url.toString();
    }

    public String generatePath(String prefix) {
        String now = DateUtil.format(new Date(), "YYYYMMDDHHmmss");
        String path = prefix + "/" + now.substring(0, 8) + "/";
        path += Convert.toInt(now.substring(8, 2)) * 3600 + Convert.toInt(now.substring(10, 2)) * 60 + Convert.toInt(now.substring(12));
        path += Math.random() + "".substring(-3) + '/';
        return path;
    }
}


# 邻里办API - 多功能社区服务平台

## 📖 项目简介

邻里办小程序API是一个基于Spring Boot的多功能社区服务平台，集成了内容管理、房屋租赁、商城购物、即时通讯、工作流等多个业务模块。平台采用前后端分离架构，支持PC端和移动端多终端访问，为社区居民提供一站式便民服务。

## ✨ 核心特性

### 🏗️ 技术架构
- **后端技术栈**：Spring Boot 2.5.8 + MyBatis Plus + Spring Security + Redis + JWT
- **前端技术栈**：Vue.js + Element UI (PC端) + Uniapp + Uview (移动端)
- **数据库**：MySQL + Redis
- **工作流引擎**：Flowable 6.7.2 + Bpmn.io
- **即时通讯**：WebSocket
- **对象存储**：腾讯云COS
- **文档预览**：kkFileView (支持doc、docx、Excel、pdf、txt、zip、rar、图片等)

### 🔐 安全特性
- JWT Token认证机制
- Spring Security权限控制
- 多种登录方式：扫码登录、验证码登录、密码登录
- 动态权限菜单、按钮权限、数据权限控制
- OAuth2三方应用授权
- XSS攻击防护

### 📊 数据可视化
- ECharts + UCharts图表展示
- DataV可视化大屏
- IReport企业级Web报表
- 系统监控：CPU、内存、磁盘、堆栈信息
- 数据库连接池监控

### 🛠️ 开发工具
- 代码生成器：一键生成前后端CRUD代码
- 在线构建器：拖拽式表单设计
- Swagger API文档自动生成
- Handsontable类Excel数据录入
- Vxe-table单行编辑即时保存

## 🏢 业务模块

### 📋 系统管理模块 (source-system)
- **用户管理**：系统用户配置、用户分类管理
- **部门管理**：组织机构树形结构、数据权限控制
- **角色管理**：角色权限分配、数据范围权限划分
- **菜单管理**：系统菜单配置、操作权限、按钮权限
- **字典管理**：系统固定数据维护
- **参数管理**：系统动态参数配置
- **通知公告**：系统消息发布维护

### 📝 内容管理模块 (source-cms)
- **文章管理**：内容发布、编辑、分类管理
- **服务项目**：社区服务内容配置
- **用户反馈**：居民意见建议收集
- **友情链接**：外部链接管理
- **文章点赞**：用户互动功能

### 🏠 房屋租赁模块 (source-house)
- **房源管理**：房屋信息发布、编辑、状态管理
- **小区管理**：小区信息维护
- **房源特色**：房屋亮点标签管理
- **图片管理**：房源图片上传、展示
- **收藏功能**：用户房源收藏、取消收藏
- **数据导入**：批量房源数据导入

### 🛒 商城模块 (source-mall)
- **商品管理**：商品信息、规格、库存管理
- **分类管理**：商品分类树形结构
- **订单管理**：订单处理、状态跟踪
- **地址管理**：收货地址维护
- **广告管理**：首页轮播图、推广内容
- **导航管理**：商城导航菜单配置

### 💬 即时通讯模块 (source-im)
- **WebSocket连接**：实时消息推送
- **在线用户管理**：用户在线状态监控
- **消息处理**：文本消息发送、接收
- **连接限制**：在线人数控制
- **客服系统**：自动回复、人工客服

### ⚙️ 工作流模块 (source-flowable)
- **流程设计**：Bpmn.io可视化流程设计
- **流程部署**：流程定义部署管理
- **任务处理**：待办任务、已办任务
- **流程监控**：流程实例跟踪

### 📱 消息模块 (source-message)
- **短信服务**：验证码发送、通知短信
- **微信集成**：公众号、小程序接入
- **消息推送**：系统消息、业务通知

### ⏰ 定时任务模块 (source-quartz)
- **任务调度**：定时任务在线管理
- **执行日志**：任务执行结果记录
- **任务监控**：任务状态实时监控

### 🔧 代码生成模块 (source-generator)
- **代码模板**：Velocity模板引擎
- **CRUD生成**：Java、HTML、XML、SQL代码生成
- **自定义配置**：字段类型、验证规则配置

## 📊 系统监控

### 📈 运行监控
- **服务监控**：CPU、内存、磁盘、堆栈信息
- **缓存监控**：Redis缓存信息、命令统计
- **连接池监控**：数据库连接池状态、SQL性能分析
- **在线用户**：当前活跃用户状态监控

### 📋 日志管理
- **操作日志**：系统操作记录、异常信息记录
- **登录日志**：用户登录记录、异常登录监控
- **系统日志**：应用运行日志、错误日志

## 🚀 快速开始

### 📋 环境要求

- **JDK**: 1.8+
- **Maven**: 3.6+
- **MySQL**: 5.7+
- **Redis**: 3.2+
- **Node.js**: 14+ (前端开发)

### 🔧 安装部署

#### 1. 克隆项目
```bash
git clone https://github.com/your-username/yjt-zenan-mp-api.git
cd yjt-zenan-mp-api
```

#### 2. 数据库配置
```sql
-- 创建数据库
CREATE DATABASE zetian DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入数据库脚本（请根据实际情况导入相应的SQL文件）
```

#### 3. 修改配置文件
编辑 `source-admin/src/main/resources/application-druid.yml`：
```yaml
spring:
  datasource:
    druid:
      master:
        url: ***********************************************************************************************************************************************
        username: your_username
        password: your_password
```

编辑 `source-admin/src/main/resources/application.yml`：
```yaml
spring:
  redis:
    host: 127.0.0.1
    port: 6379
    password: your_redis_password
    database: 2
```

#### 4. 编译运行
```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run -pl source-admin
```

#### 5. 访问系统
- **后端API**: http://localhost:12345
- **API文档**: http://localhost:12345/swagger-ui/
- **数据库监控**: http://localhost:12345/druid/

### 🔑 默认账号
- **管理员**: admin / admin123
- **数据库监控**: ruoyi / 123456

## 📁 项目结构

```
yjt-zenan-mp-api/
├── source-admin/          # 主应用模块（启动入口）
├── source-framework/      # 核心框架模块
├── source-system/         # 系统管理模块
├── source-common/         # 通用工具模块
├── source-cms/           # 内容管理模块
├── source-house/         # 房屋租赁模块
├── source-mall/          # 商城模块
├── source-im/            # 即时通讯模块
├── source-flowable/      # 工作流模块
├── source-message/       # 消息模块
├── source-quartz/        # 定时任务模块
├── source-generator/     # 代码生成模块
├── pom.xml              # 主POM文件
└── README.md            # 项目说明文档
```

## 🔌 API接口

### 🏠 房屋租赁API
```http
GET /api/houseApi/findHouseList          # 获取房源列表
GET /api/houseApi/findHouseById?id=1     # 获取房源详情
POST /api/houseApi/saveHouse             # 新增房源
POST /api/houseApi/updateHouse           # 修改房源
POST /api/houseApi/saveHeart             # 收藏/取消收藏
```

### 🛒 商城API
```http
GET /api/mallApi/findMallIndexList       # 获取商城首页数据
GET /api/mallApi/findGoodsList           # 获取商品列表
GET /api/mallApi/findGoodsById?id=1      # 获取商品详情
GET /api/mallApi/findClassifyList        # 获取商品分类
POST /api/mallApi/insertAddress          # 新增收货地址
```

### 📝 内容管理API
```http
GET /api/cmsApi/findArticleList          # 获取文章列表
GET /api/cmsApi/getArticle/{id}          # 获取文章详情
POST /api/cmsApi/starArticle             # 文章点赞
POST /api/cmsApi/saveCmsFeedback         # 提交反馈
GET /api/cmsApi/getServiceItem           # 获取服务项目
```

### 🔐 系统API
```http
GET /api/profile/getRealCity             # 获取IP城市信息
GET /api/captchaSms?loginName=phone      # 发送短信验证码
POST /login                              # 用户登录
POST /logout                             # 用户登出
```

## 🛠️ 开发指南

### 📝 代码生成器使用
1. 访问系统管理 -> 代码生成
2. 选择要生成的数据表
3. 配置生成信息（包名、作者、模块名等）
4. 点击生成代码，下载生成的代码包
5. 将代码导入到对应模块中

### 🔄 工作流配置
1. 使用Bpmn.io设计流程图
2. 部署流程定义
3. 启动流程实例
4. 处理待办任务

### 💬 WebSocket集成
```javascript
// 前端WebSocket连接示例
const ws = new WebSocket('ws://localhost:12345/websocket');
ws.onopen = function() {
    console.log('WebSocket连接成功');
};
ws.onmessage = function(event) {
    console.log('收到消息:', event.data);
};
```
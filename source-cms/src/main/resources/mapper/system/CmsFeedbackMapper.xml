<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.source.system.mapper.CmsFeedbackMapper">
    
    <resultMap type="CmsFeedback" id="CmsFeedbackResult">
        <result property="id"    column="id"    />
        <result property="feedType"    column="feed_type"    />
        <result property="feedTitle"    column="feed_title"    />
        <result property="feedContent"    column="feed_content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCmsFeedbackVo">
        select id, feed_type, feed_title, feed_content, create_by, create_time, update_by, update_time, remark from cms_feedback
    </sql>

    <select id="selectCmsFeedbackList" parameterType="CmsFeedback" resultMap="CmsFeedbackResult">
        <include refid="selectCmsFeedbackVo"/>
        <where>  
            <if test="feedType != null  and feedType != ''"> and feed_type = #{feedType}</if>
            <if test="feedTitle != null  and feedTitle != ''"> and feed_title = #{feedTitle}</if>
        </where>
    </select>
    
    <select id="selectCmsFeedbackById" parameterType="Long" resultMap="CmsFeedbackResult">
        <include refid="selectCmsFeedbackVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCmsFeedback" parameterType="CmsFeedback" useGeneratedKeys="true" keyProperty="id">
        insert into cms_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="feedType != null">feed_type,</if>
            <if test="feedTitle != null">feed_title,</if>
            <if test="feedContent != null">feed_content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="feedType != null">#{feedType},</if>
            <if test="feedTitle != null">#{feedTitle},</if>
            <if test="feedContent != null">#{feedContent},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCmsFeedback" parameterType="CmsFeedback">
        update cms_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="feedType != null">feed_type = #{feedType},</if>
            <if test="feedTitle != null">feed_title = #{feedTitle},</if>
            <if test="feedContent != null">feed_content = #{feedContent},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCmsFeedbackById" parameterType="Long">
        delete from cms_feedback where id = #{id}
    </delete>

    <delete id="deleteCmsFeedbackByIds" parameterType="String">
        delete from cms_feedback where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
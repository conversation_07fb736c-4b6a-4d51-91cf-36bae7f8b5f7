<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.source.system.mapper.CmsMsgMapper">
    
    <resultMap type="CmsMsg" id="CmsMsgResult">
        <result property="id"    column="id"    />
        <result property="msgType"    column="msg_type"    />
        <result property="msgContent"    column="msg_content"    />
        <result property="msgFromSession"    column="msg_from_session"    />
        <result property="msgFromUserId"    column="msg_from_user_id"    />
        <result property="avatar"    column="avatar"    />
        <result property="msgToSession"    column="msg_to_session"    />
        <result property="msgToUserId"    column="msg_to_user_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCmsMsgVo">
        select id, msg_type, msg_content, msg_from_session, msg_from_user_id, avatar, msg_to_session, msg_to_user_id, create_by, create_time, update_by, update_time, remark from cms_msg
    </sql>

    <select id="selectCmsMsgList" parameterType="CmsMsg" resultMap="CmsMsgResult">
        <include refid="selectCmsMsgVo"/>
        <where>  
            <if test="msgType != null "> and msg_type = #{msgType}</if>
            <if test="msgContent != null  and msgContent != ''"> and msg_content = #{msgContent}</if>
        </where>
    </select>
    
    <select id="selectCmsMsgById" parameterType="Long" resultMap="CmsMsgResult">
        <include refid="selectCmsMsgVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCmsMsg" parameterType="CmsMsg" useGeneratedKeys="true" keyProperty="id">
        insert into cms_msg
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="msgType != null">msg_type,</if>
            <if test="msgContent != null and msgContent != ''">msg_content,</if>
            <if test="msgFromSession != null">msg_from_session,</if>
            <if test="msgFromUserId != null">msg_from_user_id,</if>
            <if test="avatar != null">avatar,</if>
            <if test="msgToSession != null">msg_to_session,</if>
            <if test="msgToUserId != null">msg_to_user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="msgType != null">#{msgType},</if>
            <if test="msgContent != null and msgContent != ''">#{msgContent},</if>
            <if test="msgFromSession != null">#{msgFromSession},</if>
            <if test="msgFromUserId != null">#{msgFromUserId},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="msgToSession != null">#{msgToSession},</if>
            <if test="msgToUserId != null">#{msgToUserId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCmsMsg" parameterType="CmsMsg">
        update cms_msg
        <trim prefix="SET" suffixOverrides=",">
            <if test="msgType != null">msg_type = #{msgType},</if>
            <if test="msgContent != null and msgContent != ''">msg_content = #{msgContent},</if>
            <if test="msgFromSession != null">msg_from_session = #{msgFromSession},</if>
            <if test="msgFromUserId != null">msg_from_user_id = #{msgFromUserId},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="msgToSession != null">msg_to_session = #{msgToSession},</if>
            <if test="msgToUserId != null">msg_to_user_id = #{msgToUserId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCmsMsgById" parameterType="Long">
        delete from cms_msg where id = #{id}
    </delete>

    <delete id="deleteCmsMsgByIds" parameterType="String">
        delete from cms_msg where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.source.system.mapper.CmsLinkMapper">
    
    <resultMap type="CmsLink" id="CmsLinkResult">
        <result property="id"    column="id"    />
        <result property="linkName"    column="link_Name"    />
        <result property="linkUrl"    column="link_Url"    />
        <result property="linkLogo"    column="link_Logo"    />
        <result property="linkShow"    column="link_Show"    />
        <result property="linkWay"    column="link_Way"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCmsLinkVo">
        select id, link_Name, link_Url, link_Logo, link_Show, link_Way, create_by, create_time, update_by, update_time, remark from cms_link
    </sql>

    <select id="selectCmsLinkList" parameterType="CmsLink" resultMap="CmsLinkResult">
        <include refid="selectCmsLinkVo"/>
        <where>  
            <if test="linkName != null  and linkName != ''"> and link_Name like concat('%', #{linkName}, '%')</if>
            <if test="linkWay != null  and linkWay != ''"> and link_Way = #{linkWay}</if>
        </where>
    </select>
    
    <select id="selectCmsLinkById" parameterType="Long" resultMap="CmsLinkResult">
        <include refid="selectCmsLinkVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCmsLink" parameterType="CmsLink" useGeneratedKeys="true" keyProperty="id">
        insert into cms_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="linkName != null">link_Name,</if>
            <if test="linkUrl != null">link_Url,</if>
            <if test="linkLogo != null">link_Logo,</if>
            <if test="linkShow != null">link_Show,</if>
            <if test="linkWay != null">link_Way,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="linkName != null">#{linkName},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
            <if test="linkLogo != null">#{linkLogo},</if>
            <if test="linkShow != null">#{linkShow},</if>
            <if test="linkWay != null">#{linkWay},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCmsLink" parameterType="CmsLink">
        update cms_link
        <trim prefix="SET" suffixOverrides=",">
            <if test="linkName != null">link_Name = #{linkName},</if>
            <if test="linkUrl != null">link_Url = #{linkUrl},</if>
            <if test="linkLogo != null">link_Logo = #{linkLogo},</if>
            <if test="linkShow != null">link_Show = #{linkShow},</if>
            <if test="linkWay != null">link_Way = #{linkWay},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCmsLinkById" parameterType="Long">
        delete from cms_link where id = #{id}
    </delete>

    <delete id="deleteCmsLinkByIds" parameterType="String">
        delete from cms_link where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
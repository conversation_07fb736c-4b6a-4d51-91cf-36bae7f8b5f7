<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.source.system.mapper.CmsServiceItemMapper">

    <resultMap type="CmsServiceItem" id="CmsServiceItemResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="state"    column="state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCmsServiceItemVo">
        select id, type, title, content, state, create_by, create_time, update_by, update_time, remark from cms_service_item
    </sql>

    <select id="selectCmsServiceItemList" parameterType="CmsServiceItem" resultMap="CmsServiceItemResult">
        <include refid="selectCmsServiceItemVo"/>
        <where>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="title != null and type != ''">and title = #{title}</if>
        </where>
    </select>

    <select id="selectCmsServiceItemById" parameterType="Long" resultMap="CmsServiceItemResult">
        <include refid="selectCmsServiceItemVo"/>
        where id = #{id}
    </select>

    <insert id="insertCmsServiceItem" parameterType="CmsServiceItem">
        insert into cms_service_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="type != null">type,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="state != null">state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null">#{type},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="state != null">#{state},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCmsServiceItem" parameterType="CmsServiceItem">
        update cms_service_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCmsServiceItemById" parameterType="Long">
        delete from cms_service_item where id = #{id}
    </delete>

    <delete id="deleteCmsServiceItemByIds" parameterType="String">
        delete from cms_service_item where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
